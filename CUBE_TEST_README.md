# ROV立方体测试版本 - ActionGraph转换验证

## 概述

此版本是ROV独立Python应用程序的测试版本，专门用于验证ActionGraph到Python的转换是否正确工作。主要变更包括：

1. **目标文件更改**: 从 `ROV_THRUSTERS.usd` 更改为 `ROV_TEST.usd`
2. **目标对象更改**: 从 `/World/ROV/Thrusters/*` 更改为 `/World/cube`
3. **验证目标**: 确保ActionGraph转换的Python类能够正确复制原始ActionGraph的物理行为

## 主要更改

### 1. USD文件路径更新
```python
# 原来
usd_path = os.path.abspath("/home/<USER>/self_underwater_isaacsim/ROV_THRUSTERS.usd")

# 现在
usd_path = os.path.abspath("ROV_TEST.usd")
```

### 2. 刚体目标路径更新
```python
# 原来的路径优先级
test_paths = [
    "/World/ROV/Thrusters/*",
    "/World/ROV/Thrusters/left_front",
    "/World/ROV/*",
]

# 现在的路径优先级
test_paths = [
    "/World/cube",                      # 最优先：立方体对象
    "/World/Cube",                      # 备选：大写的Cube
    "/World/*/cube",                    # 通配符查找cube
    "/World/*/Cube",                    # 通配符查找Cube
]
```

### 3. ActionGraph节点验证

测试文件包含两个主要的ActionGraph工作流：

#### 浮力ActionGraph (`/World/Cube/buoyancy`)
- **输入**: volume, height, z_position, rotation
- **处理**: 计算带旋转的浮力
- **输出**: x_force, y_force, z_force
- **Python类**: `BuoyancyForcesNode`

#### 阻尼ActionGraph (`/World/Cube/damping`)
- **输入**: z_position, max_damping, floating_obj_height
- **处理**: 基于位置的阻尼计算
- **输出**: linear_damping, angular_damping
- **Python类**: `DampingNode`

## 文件结构

```
isaac_underwater/
├── rov_standalone.py          # 主仿真文件（已更新）
├── ROV_TEST.usd              # 新的测试USD文件
├── BUOYANCY_TEST.usda        # 参考ActionGraph实现
├── test_cube_simulation.py   # 测试脚本
├── CUBE_TEST_README.md       # 本文档
└── rov_classes/              # ActionGraph转换的Python类
    ├── buoyancy_forces.py    # 浮力计算
    ├── damping.py           # 阻尼计算
    ├── controller.py        # PID控制器
    └── ...
```

## 使用方法

### 1. 运行测试验证
```bash
# 验证所有组件是否正常工作
python test_cube_simulation.py
```

### 2. 运行Isaac Sim仿真
```bash
# 在Isaac Sim环境中运行
python rov_standalone.py
```

## 测试结果

运行 `test_cube_simulation.py` 应该显示：

```
🧪 立方体仿真测试 - ActionGraph转换验证
==================================================

✅ USD文件存在性: 通过
✅ 立方体物理类: 通过  
✅ ActionGraph转换: 通过

🎉 所有测试通过！立方体仿真准备就绪。
```

## 验证要点

### 1. 浮力计算验证
- 立方体部分浸没时应产生向上的浮力
- 旋转时浮力方向应相应改变
- 浮力大小应与浸没体积成正比

### 2. 阻尼计算验证
- 物体在水面上方时阻尼应很小
- 物体完全浸没时阻尼应达到最大值
- 部分浸没时阻尼应与浸没程度成正比

### 3. 物理行为一致性
- Python实现应产生与ActionGraph相同的力和阻尼值
- 立方体的运动行为应与原始ActionGraph版本一致

## 预期行为

在Isaac Sim中运行时，立方体应该：

1. **浮力效应**: 当立方体下沉到水中时，应受到向上的浮力
2. **阻尼效应**: 在水中运动时应受到阻尼力的影响
3. **旋转响应**: 倾斜时浮力方向应相应调整
4. **稳定性**: 系统应保持稳定，不会出现异常振荡

## 故障排除

### 常见问题

1. **找不到立方体对象**
   - 检查 `ROV_TEST.usd` 文件是否存在
   - 确认立方体路径是否为 `/World/cube`

2. **物理计算异常**
   - 运行 `test_cube_simulation.py` 验证Python类
   - 检查浮力和阻尼参数是否合理

3. **Isaac Sim连接问题**
   - 确保Isaac Sim环境正确设置
   - 检查所有必要的模块是否已导入

## 下一步

1. 在Isaac Sim中验证立方体物理行为
2. 对比ActionGraph和Python实现的结果
3. 确认转换的准确性
4. 如果测试通过，可以将相同的转换方法应用到完整的ROV系统

## 技术细节

### ActionGraph vs Python映射

| ActionGraph节点 | Python类 | 功能 |
|----------------|----------|------|
| buoyancy/script_node | BuoyancyForcesNode | 带旋转的浮力计算 |
| damping/script_node | DampingNode | 位置相关的阻尼计算 |
| controller | ControllerNode | PID控制器 |
| quat_to_euler | QuatToEulerNode | 四元数转欧拉角 |

### 参数对应关系

- **volume**: 立方体体积 (m³)
- **height**: 立方体高度 (m)
- **z_position**: 立方体Z坐标 (m)
- **rotation**: 旋转角度 [roll, pitch, yaw] (度)
- **max_damping**: 最大阻尼系数
- **floating_obj_height**: 浮动对象高度 (m)

这个测试版本为验证ActionGraph到Python转换的正确性提供了一个简化但完整的测试环境。
