#!/usr/bin/env python3
"""
简化的ROV修复测试
测试修复后的rov_standalone.py是否能正确处理推进器路径
"""

import os
import sys

def test_rov_import():
    """测试ROV类导入"""
    try:
        print("🔍 测试ROV类导入...")
        
        # 添加当前目录到Python路径
        sys.path.append('.')
        
        # 尝试导入ROV类（不初始化Isaac Sim）
        print("  导入rov_classes模块...")
        from rov_classes.buoyancy_forces import BuoyancyForces
        from rov_classes.controller import Controller
        from rov_classes.linear_angular_control import LinearAngularControl
        print("  ✅ ROV类导入成功")

        # 测试类实例化
        print("  测试类实例化...")
        buoyancy = BuoyancyForces()
        controller = Controller()
        angular = LinearAngularControl()
        print("  ✅ 类实例化成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 导入失败: {e}")
        return False


def test_usd_file():
    """测试USD文件存在性"""
    print("🔍 测试USD文件...")
    
    usd_path = "/home/<USER>/self_underwater_isaacsim/ROV_THRUSTERS.usd"
    
    if os.path.exists(usd_path):
        size_mb = os.path.getsize(usd_path) / (1024 * 1024)
        print(f"  ✅ USD文件存在: {usd_path}")
        print(f"  📊 文件大小: {size_mb:.1f} MB")
        return True
    else:
        print(f"  ❌ USD文件不存在: {usd_path}")
        return False


def analyze_log_info():
    """基于用户提供的日志信息分析"""
    print("🔍 基于日志信息分析...")
    
    # 从用户日志中提取的信息
    thruster_paths = [
        "/World/ROV/Thrusters/right_back",
        "/World/ROV/Thrusters/left_front", 
        "/World/ROV/Thrusters/right_front",
        "/World/ROV/Thrusters/left_back"
    ]
    
    actiongraph_paths = [
        "/World/ROV/ActionGraph/angular_control/make_3_vector",
        "/World/ROV/ActionGraph/buoyancy_L/make_3_vector",
        "/World/ROV/ActionGraph/buoyancy_R/make_3_vector"
    ]
    
    print(f"  📍 发现的推进器路径 ({len(thruster_paths)}个):")
    for path in thruster_paths:
        print(f"    - {path}")
    
    print(f"  🔧 发现的ActionGraph节点 ({len(actiongraph_paths)}个):")
    for path in actiongraph_paths:
        print(f"    - {path}")
    
    print(f"  💡 分析结论:")
    print(f"    - ROV有4个推进器，都是刚体对象")
    print(f"    - 推进器路径模式: /World/ROV/Thrusters/*")
    print(f"    - 原有ActionGraph功能已转换为Python类")
    print(f"    - 建议使用推进器路径作为RigidPrimView目标")
    
    return True


def test_path_strategy():
    """测试路径策略"""
    print("🔍 测试路径策略...")
    
    # 模拟修复后的路径尝试顺序
    test_paths = [
        "/World/ROV/Thrusters/*",           # 最优先：推进器通配符
        "/World/ROV/Thrusters/left_front",  # 单个推进器测试
        "/World/ROV/*",                     # 所有子对象通配符
        "/World/ROV",                       # 直接使用ROV对象
    ]
    
    print(f"  📋 修复后的路径尝试顺序:")
    for i, path in enumerate(test_paths, 1):
        print(f"    {i}. {path}")
    
    print(f"  💡 策略说明:")
    print(f"    - 优先尝试推进器路径（基于日志信息）")
    print(f"    - 使用通配符匹配多个刚体")
    print(f"    - 提供备选路径确保兼容性")
    print(f"    - 详细日志帮助调试")
    
    return True


def main():
    """主函数"""
    print("🧪 ROV修复简化测试")
    print("="*60)
    
    tests = [
        ("ROV类导入测试", test_rov_import),
        ("USD文件检查", test_usd_file), 
        ("日志信息分析", analyze_log_info),
        ("路径策略测试", test_path_strategy),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 40)
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n" + "="*60)
    print("测试结果总结")
    print("="*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"📊 测试统计: {passed}/{total} 通过")
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status} {test_name}")
    
    if passed == total:
        print(f"\n🎉 所有测试通过！")
        print(f"💡 建议:")
        print(f"  1. 运行 python rov_standalone.py 测试完整功能")
        print(f"  2. 查看详细日志确认推进器路径识别")
        print(f"  3. 如有问题，检查Isaac Sim环境设置")
    else:
        print(f"\n⚠️  部分测试失败")
        print(f"💡 建议:")
        print(f"  1. 检查失败的测试项目")
        print(f"  2. 确认Isaac Sim环境正确安装")
        print(f"  3. 验证USD文件路径和权限")


if __name__ == "__main__":
    main()
