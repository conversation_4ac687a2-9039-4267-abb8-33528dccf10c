#!/usr/bin/env python3
"""
ROV水下仿真 - Standalone模式 (立方体测试版本)

此版本用于验证ActionGraph到Python的转换是否正确工作。
- 目标文件: ROV_TEST.usd
- 目标对象: /World/cube (立方体)
- 测试内容: 浮力和阻尼ActionGraph的Python实现

使用Python类替代ActionGraph实现物理仿真，确保转换后的行为与原始ActionGraph一致。
"""

# Isaac Sim标准初始化代码
from isaacsim import SimulationApp
simulation_app = SimulationApp({"headless": False})

import os
import numpy as np
import omni.usd
from omni.isaac.core import World
from omni.isaac.core.utils.stage import add_reference_to_stage
from omni.isaac.core.utils.prims import get_prim_at_path
from omni.isaac.core.prims import RigidPrimView
import carb

# USD和物理相关导入
try:
    from pxr import UsdPhysics, Usd
except ImportError:
    carb.log_warn("无法导入pxr模块，某些功能可能受限")

# 导入我们的ROV类
from rov_classes import (
    BuoyancyForcesNode,
    BuoyancyControlNode, 
    DampingNode,
    ControllerNode,
    LinearAngularControlNode,
    QuatToEulerNode
)


class ROVSimulation:
    """
    ROV仿真主类 - 立方体测试版本

    此版本用于测试ActionGraph到Python的转换，目标对象是立方体(/World/cube)
    而不是原来的ROV推进器。这是一个验证测试，确保浮力和阻尼计算正确工作。
    """

    def __init__(self):
        """初始化ROV仿真（立方体测试版本）"""
        self.world = None
        self.rov_prim = None
        self.rov_view = None

        # 初始化物理计算节点（从ActionGraph转换而来）
        self.buoyancy_forces_node = BuoyancyForcesNode("BuoyancyForces")
        self.buoyancy_control_node = BuoyancyControlNode("BuoyancyControl")
        self.damping_node = DampingNode("Damping")
        self.controller_node = ControllerNode("Controller")
        self.thruster_control_node = LinearAngularControlNode("ThrusterControl")
        self.quat_to_euler_node = QuatToEulerNode("QuatToEuler")

        # 立方体对象参数（用于测试）
        self.rov_volume = 0.1  # m³ - 立方体体积
        self.rov_height = 0.2  # m - 立方体高度

        # 控制输入
        self.joystick_x = 0.0
        self.joystick_y = 0.0
        self.dive_input = 0.0
        
    def setup_world(self):
        """设置仿真世界"""
        # 先不创建World对象，等USD加载后再创建
        pass

    def load_usd_scene(self, usd_path: str):
        """
        加载USD场景文件

        Args:
            usd_path: USD文件的绝对路径
        """
        if not os.path.exists(usd_path):
            carb.log_error(f"USD文件不存在: {usd_path}")
            return False

        try:
            # 使用omni.usd.get_context().open_stage()打开USD文件
            context = omni.usd.get_context()
            context.open_stage(usd_path)
            carb.log_info(f"成功加载USD文件: {usd_path}")

            # 等待一帧让USD完全加载
            simulation_app.update()

            # 现在创建World对象
            self.world = World(stage_units_in_meters=1.0)

            # 重置世界以初始化场景
            self.world.reset()

            return True
        except Exception as e:
            carb.log_error(f"加载USD文件失败: {e}")
            return False
    
    def analyze_usd_structure(self):
        """分析USD场景结构，找到刚体对象（特别是立方体）"""
        try:
            context = omni.usd.get_context()
            stage = context.get_stage()

            carb.log_info("分析USD场景结构，寻找立方体对象...")

            # 遍历场景中的所有prim
            rigid_body_paths = []
            cube_related_paths = []
            rov_related_paths = []

            for prim in stage.Traverse():
                prim_path = prim.GetPath().pathString
                prim_type = prim.GetTypeName()

                # 优先查找立方体对象
                if "cube" in prim_path.lower() or "Cube" in prim_path:
                    carb.log_info(f"发现立方体对象: {prim_path} (类型: {prim_type})")
                    cube_related_paths.append(prim_path)
                    # 立方体通常是刚体对象
                    if prim_type in ["Mesh", "Cube", "Xform"] or "rigid" in prim_type.lower():
                        carb.log_info(f"  -> 立方体刚体对象: {prim_path}")
                        rigid_body_paths.append(prim_path)

                # 检查是否是ROV相关的对象（作为备选）
                elif "/World/ROV" in prim_path:
                    carb.log_info(f"发现ROV相关对象: {prim_path} (类型: {prim_type})")
                    rov_related_paths.append(prim_path)

                    # 特别关注推进器对象（基于日志信息）
                    if "/Thrusters/" in prim_path:
                        carb.log_info(f"  -> 推进器对象: {prim_path}")
                        rigid_body_paths.append(prim_path)
                    # 简单的刚体检测：检查类型名称
                    elif prim_type in ["RigidBody", "Cube", "Sphere", "Cylinder", "Mesh", "Xform"] or "rigid" in prim_type.lower():
                        carb.log_info(f"  -> 可能是刚体对象: {prim_path}")
                        rigid_body_paths.append(prim_path)
                    else:
                        carb.log_info(f"  -> 容器对象: {prim_path}")

            # 如果没有找到立方体，尝试常见的立方体路径
            if not cube_related_paths:
                carb.log_info("未找到立方体对象，尝试常见的立方体路径...")
                common_cube_paths = ["/World/cube", "/World/Cube", "/World/test_cube", "/World/TestCube"]

                for cube_path in common_cube_paths:
                    test_prim = get_prim_at_path(cube_path)
                    if test_prim and test_prim.IsValid():
                        carb.log_info(f"找到立方体对象: {cube_path}")
                        rigid_body_paths.append(cube_path)

            # 如果没有找到明确的刚体，尝试常见的子路径
            if not rigid_body_paths and rov_related_paths:
                carb.log_info("未找到明确的刚体，尝试常见的子路径...")
                common_child_names = ["body", "hull", "chassis", "base", "main"]

                for child_name in common_child_names:
                    test_path = f"/World/ROV/{child_name}"
                    test_prim = get_prim_at_path(test_path)
                    if test_prim and test_prim.IsValid():
                        carb.log_info(f"找到子对象: {test_path}")
                        rigid_body_paths.append(test_path)

            return rigid_body_paths

        except Exception as e:
            carb.log_error(f"分析USD结构失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def setup_rov(self):
        """设置ROV对象 - 现在目标是立方体对象"""
        try:
            # 先分析USD结构，找到真正的刚体对象
            rigid_body_paths = self.analyze_usd_structure()

            # 定义要尝试的路径列表（按优先级排序）
            # 现在主要目标是立方体对象 /World/cube
            test_paths = [
                "/World/cube",                      # 最优先：立方体对象
                "/World/Cube",                      # 备选：大写的Cube
                "/World/*/cube",                    # 通配符查找cube
                "/World/*/Cube",                    # 通配符查找Cube
            ]

            # 如果分析找到了刚体路径，添加到列表
            if rigid_body_paths:
                # 将分析发现的路径插入到前面
                for path in reversed(rigid_body_paths):
                    if path not in test_paths:
                        test_paths.insert(0, path)

            # 添加其他备选路径（保留原有ROV路径作为后备）
            backup_paths = [
                "/World/ROV/Thrusters/*",           # 原有推进器路径
                "/World/ROV/Thrusters/left_front",
                "/World/ROV/*",
                "/World/ROV",
                "/World/ROV/Thrusters/right_front",
                "/World/ROV/Thrusters/left_back",
                "/World/ROV/Thrusters/right_back",
                "/World/ROV/body",
                "/World/ROV/hull",
                "/World/ROV/chassis",
                "/World/ROV/base",
                "/World/ROV/main"
            ]

            # 避免重复路径
            for path in backup_paths:
                if path not in test_paths:
                    test_paths.append(path)



            carb.log_info(f"将尝试 {len(test_paths)} 个路径...")

            # 逐个尝试路径
            for i, test_path in enumerate(test_paths):
                try:
                    carb.log_info(f"尝试路径 {i+1}/{len(test_paths)}: {test_path}")

                    # 创建RigidPrimView
                    test_view = RigidPrimView(
                        prim_paths_expr=test_path,
                        name="rov_view"
                    )

                    # 尝试添加到场景
                    self.world.scene.add(test_view)

                    # 验证是否成功创建
                    try:
                        count = test_view.count
                        carb.log_info(f"  ✅ 成功！找到 {count} 个刚体对象")

                        # 如果成功，保存并返回
                        self.rov_view = test_view
                        self.rigid_body_paths = [test_path]
                        carb.log_info(f"ROV设置完成，使用路径: {test_path}")
                        return True

                    except Exception as e:
                        carb.log_warn(f"  ⚠️  创建成功但验证失败: {e}")
                        # 即使验证失败，如果创建成功也可以使用
                        self.rov_view = test_view
                        self.rigid_body_paths = [test_path]
                        carb.log_info(f"ROV设置完成（验证失败但可用），使用路径: {test_path}")
                        return True

                except Exception as e:
                    carb.log_info(f"  ❌ 失败: {str(e)[:100]}...")
                    continue

            # 所有路径都失败
            carb.log_error("所有路径都失败了！")
            carb.log_error("可能的原因:")
            carb.log_error("1. USD文件中的ROV对象没有正确的物理设置")
            carb.log_error("2. ROV对象路径不是 /World/ROV")
            carb.log_error("3. 需要在Isaac Sim中为ROV添加RigidBodyAPI")
            return False

        except Exception as e:
            carb.log_error(f"ROV设置失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def get_rov_state(self):
        """获取ROV当前状态"""
        if self.rov_view is None:
            carb.log_warn("rov_view为None，返回默认状态")
            # 返回默认状态以避免程序崩溃
            return {
                'position': np.array([0.0, 0.0, 0.0]),
                'orientation': np.array([0.0, 0.0, 0.0, 1.0]),
                'z_position': 0.0
            }

        try:
            # 获取位置和旋转
            poses = self.rov_view.get_world_poses()
            positions = poses[0]
            orientations = poses[1]

            if positions is not None and len(positions) > 0:
                position = positions[0]
                orientation = orientations[0]

                return {
                    'position': position,
                    'orientation': orientation,
                    'z_position': float(position[2])
                }
            else:
                carb.log_warn("未获取到有效的位置数据，使用默认值")
                return {
                    'position': np.array([0.0, 0.0, 0.0]),
                    'orientation': np.array([0.0, 0.0, 0.0, 1.0]),
                    'z_position': 0.0
                }

        except Exception as e:
            carb.log_warn(f"获取ROV状态失败: {e}，使用默认值")
            return {
                'position': np.array([0.0, 0.0, 0.0]),
                'orientation': np.array([0.0, 0.0, 0.0, 1.0]),
                'z_position': 0.0
            }
    
    def compute_forces(self, rov_state):
        """计算ROV受力"""
        if rov_state is None:
            return
        
        # 转换四元数到欧拉角
        self.quat_to_euler_node.set_inputs(
            quaternion=[
                float(rov_state['orientation'][0]),
                float(rov_state['orientation'][1]), 
                float(rov_state['orientation'][2]),
                float(rov_state['orientation'][3])
            ]
        )
        euler_output = self.quat_to_euler_node.execute()
        rotation = euler_output.get('rotation', [0.0, 0.0, 0.0])
        
        # 计算浮力（带旋转）
        self.buoyancy_forces_node.set_inputs(
            volume=self.rov_volume,
            height=self.rov_height,
            z_position=rov_state['z_position'],
            rotation=rotation
        )
        buoyancy_output = self.buoyancy_forces_node.execute()
        
        # 计算阻尼
        self.damping_node.set_inputs(
            z_position=rov_state['z_position'],
            max_damping=1.0,
            floating_obj_height=self.rov_height
        )
        damping_output = self.damping_node.execute()
        
        # 计算推进器控制
        self.thruster_control_node.set_inputs(
            y_stick=self.joystick_y,
            x_stick=self.joystick_x
        )
        thruster_output = self.thruster_control_node.execute()
        
        # 计算PID控制（使用pitch角度）
        pitch_angle = rotation[1] if len(rotation) > 1 else 0.0
        self.controller_node.set_inputs(
            orientation=pitch_angle,
            dive_force=self.dive_input
        )
        controller_output = self.controller_node.execute()
        
        return {
            'buoyancy': buoyancy_output,
            'damping': damping_output,
            'thruster': thruster_output,
            'controller': controller_output,
            'rotation': rotation
        }
    
    def apply_forces(self, forces):
        """应用计算出的力到ROV"""
        if self.rov_view is None:
            carb.log_warn("rov_view为None，无法应用力")
            return

        if forces is None:
            carb.log_warn("forces为None，跳过力应用")
            return

        try:
            # 组合所有力
            total_force = np.array([0.0, 0.0, 0.0])

            # 添加浮力
            buoyancy = forces.get('buoyancy', {})
            buoyancy_force = np.array([
                buoyancy.get('x_force', 0.0),
                buoyancy.get('y_force', 0.0),
                buoyancy.get('z_force', 0.0)
            ])
            total_force += buoyancy_force

            # 添加推进器力（简化处理，实际应该分别应用到各个推进器）
            thruster = forces.get('thruster', {})
            left_front = thruster.get('left_front', [0, 0, 0])
            right_front = thruster.get('right_front', [0, 0, 0])
            left_back = thruster.get('left_back', [0, 0, 0])
            right_back = thruster.get('right_back', [0, 0, 0])

            # 合并推进器力
            thruster_force = np.array(left_front) + np.array(right_front) + \
                           np.array(left_back) + np.array(right_back)
            total_force += thruster_force

            # 添加控制器力
            controller = forces.get('controller', {})
            controller_force = controller.get('force', [0, 0, 0])
            if isinstance(controller_force, (list, tuple, np.ndarray)) and len(controller_force) >= 3:
                total_force += np.array(controller_force[:3])

            # 尝试应用力
            try:
                # 获取刚体数量
                count = self.rov_view.count
                carb.log_info(f"应用力到 {count} 个刚体: [{total_force[0]:.2f}, {total_force[1]:.2f}, {total_force[2]:.2f}]")

                # 为每个刚体创建相同的力
                if count > 0:
                    forces_tensor = np.tile(total_force, (count, 1))
                    self.rov_view.apply_forces(forces_tensor)
                else:
                    carb.log_warn("刚体数量为0，无法应用力")

            except Exception as e:
                carb.log_warn(f"无法获取刚体数量: {e}，尝试直接应用力")
                # 尝试简单的单个力应用
                forces_tensor = total_force.reshape(1, 3)
                self.rov_view.apply_forces(forces_tensor)
                carb.log_info(f"使用单个力应用: [{total_force[0]:.2f}, {total_force[1]:.2f}, {total_force[2]:.2f}]")

        except Exception as e:
            carb.log_error(f"应用力失败: {e}")
            import traceback
            traceback.print_exc()
    
    def update_controls(self):
        """更新控制输入（这里可以添加键盘/手柄输入）"""
        # 简单的键盘控制示例
        # 实际使用中可以集成手柄输入
        pass
    
    def step(self):
        """仿真步进"""
        if self.world is None or self.rov_view is None:
            return

        # 更新控制输入
        self.update_controls()

        # 获取ROV状态
        rov_state = self.get_rov_state()

        # 计算力
        forces = self.compute_forces(rov_state)

        # 应用力
        self.apply_forces(forces)

        # 步进仿真
        self.world.step(render=True)


def main():
    """主函数"""
    # 创建ROV仿真实例
    rov_sim = ROVSimulation()

    # 加载USD场景 - 使用新的测试文件ROV_TEST.usd
    usd_path = os.path.abspath("ROV_TEST.usd")
    if not rov_sim.load_usd_scene(usd_path):
        carb.log_error("无法加载USD场景，退出")
        simulation_app.close()
        return

    # 设置ROV（现在目标是立方体对象）
    if not rov_sim.setup_rov():
        carb.log_error("无法设置ROV，退出")
        simulation_app.close()
        return

    carb.log_info("ROV仿真启动成功，开始仿真循环...")

    # 仿真循环
    try:
        while simulation_app.is_running():
            rov_sim.step()
    except KeyboardInterrupt:
        carb.log_info("用户中断仿真")
    except Exception as e:
        carb.log_error(f"仿真错误: {e}")
    finally:
        simulation_app.close()


if __name__ == "__main__":
    main()
