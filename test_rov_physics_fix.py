#!/usr/bin/env python3
"""
测试ROV物理修复
验证修复后的rov_standalone.py是否能正确处理刚体设置
"""

# Isaac Sim标准初始化代码
from isaacsim import SimulationApp
simulation_app = SimulationApp({"headless": True})

import os
import sys
import time
import carb

# 添加当前目录到Python路径
sys.path.append('.')

def test_rov_physics():
    """测试ROV物理设置"""
    
    try:
        # 导入ROV仿真类
        from rov_standalone import ROVSimulation
        
        carb.log_info("开始测试ROV物理修复...")
        
        # 创建ROV仿真实例
        rov_sim = ROVSimulation()
        
        # 测试USD文件路径
        usd_path = "/home/<USER>/self_underwater_isaacsim/ROV_THRUSTERS.usd"
        
        if not os.path.exists(usd_path):
            carb.log_error(f"USD文件不存在: {usd_path}")
            return False
        
        carb.log_info(f"测试USD文件: {usd_path}")
        
        # 步骤1: 加载USD场景
        carb.log_info("步骤1: 加载USD场景...")
        if not rov_sim.load_usd_scene(usd_path):
            carb.log_error("USD场景加载失败")
            return False
        carb.log_info("✅ USD场景加载成功")
        
        # 步骤2: 设置ROV
        carb.log_info("步骤2: 设置ROV...")
        if not rov_sim.setup_rov():
            carb.log_error("ROV设置失败")
            return False
        carb.log_info("✅ ROV设置成功")
        
        # 步骤3: 测试状态获取
        carb.log_info("步骤3: 测试状态获取...")
        rov_state = rov_sim.get_rov_state()
        if rov_state is None:
            carb.log_error("无法获取ROV状态")
            return False
        
        carb.log_info(f"✅ ROV状态获取成功:")
        carb.log_info(f"   位置: {rov_state['position']}")
        carb.log_info(f"   姿态: {rov_state['orientation']}")
        carb.log_info(f"   Z位置: {rov_state['z_position']}")
        
        # 步骤4: 测试力计算
        carb.log_info("步骤4: 测试力计算...")
        forces = rov_sim.compute_forces(rov_state)
        if forces is None:
            carb.log_error("力计算失败")
            return False
        
        carb.log_info("✅ 力计算成功:")
        for force_type, force_data in forces.items():
            carb.log_info(f"   {force_type}: {force_data}")
        
        # 步骤5: 测试力应用
        carb.log_info("步骤5: 测试力应用...")
        try:
            rov_sim.apply_forces(forces)
            carb.log_info("✅ 力应用成功")
        except Exception as e:
            carb.log_error(f"力应用失败: {e}")
            return False
        
        # 步骤6: 运行几步仿真
        carb.log_info("步骤6: 运行短期仿真测试...")
        for i in range(10):
            try:
                rov_sim.step()
                if i % 3 == 0:
                    carb.log_info(f"   仿真步骤 {i+1}/10 完成")
            except Exception as e:
                carb.log_error(f"仿真步骤 {i+1} 失败: {e}")
                return False
        
        carb.log_info("✅ 短期仿真测试成功")
        
        # 测试完成
        carb.log_info("🎉 所有测试通过！ROV物理修复成功！")
        return True
        
    except Exception as e:
        carb.log_error(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🧪 ROV物理修复测试")
    print("="*50)
    
    try:
        success = test_rov_physics()
        
        if success:
            print("\n✅ 测试结果: 成功")
            print("ROV物理设置已修复，可以正常运行仿真")
        else:
            print("\n❌ 测试结果: 失败")
            print("ROV物理设置仍有问题，需要进一步调试")
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        simulation_app.close()


if __name__ == "__main__":
    main()
