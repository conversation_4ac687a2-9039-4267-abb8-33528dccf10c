"""
基础节点类，用于将ActionGraph节点转换为Python类
提供统一的接口和数据传递机制
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional
import numpy as np


class NodeInputs:
    """节点输入数据容器"""
    
    def __init__(self):
        self._data = {}
    
    def __getattr__(self, name: str) -> Any:
        """获取输入属性"""
        if name.startswith('_'):
            return super().__getattribute__(name)
        return self._data.get(name, None)
    
    def __setattr__(self, name: str, value: Any) -> None:
        """设置输入属性"""
        if name.startswith('_'):
            super().__setattr__(name, value)
        else:
            if not hasattr(self, '_data'):
                super().__setattr__('_data', {})
            self._data[name] = value
    
    def set_input(self, name: str, value: Any) -> None:
        """设置输入值"""
        self._data[name] = value
    
    def get_input(self, name: str, default: Any = None) -> Any:
        """获取输入值"""
        return self._data.get(name, default)


class NodeOutputs:
    """节点输出数据容器"""
    
    def __init__(self):
        self._data = {}
    
    def __getattr__(self, name: str) -> Any:
        """获取输出属性"""
        if name.startswith('_'):
            return super().__getattribute__(name)
        return self._data.get(name, None)
    
    def __setattr__(self, name: str, value: Any) -> None:
        """设置输出属性"""
        if name.startswith('_'):
            super().__setattr__(name, value)
        else:
            if not hasattr(self, '_data'):
                super().__setattr__('_data', {})
            self._data[name] = value
    
    def set_output(self, name: str, value: Any) -> None:
        """设置输出值"""
        self._data[name] = value
    
    def get_output(self, name: str, default: Any = None) -> Any:
        """获取输出值"""
        return self._data.get(name, default)
    
    def get_all_outputs(self) -> Dict[str, Any]:
        """获取所有输出值"""
        return self._data.copy()


class BaseNode(ABC):
    """ActionGraph节点的基础类"""
    
    def __init__(self, name: str = ""):
        """
        初始化节点
        
        Args:
            name: 节点名称
        """
        self.name = name
        self.inputs = NodeInputs()
        self.outputs = NodeOutputs()
        self._initialized = False
        
        # 调用setup方法进行初始化
        self.setup()
        self._initialized = True
    
    @abstractmethod
    def setup(self) -> None:
        """
        节点初始化方法，对应ActionGraph的setup函数
        子类必须实现此方法
        """
        pass
    
    @abstractmethod
    def compute(self) -> None:
        """
        节点计算方法，对应ActionGraph的compute函数
        子类必须实现此方法
        """
        pass
    
    def execute(self) -> Dict[str, Any]:
        """
        执行节点计算并返回输出
        
        Returns:
            节点的所有输出值
        """
        if not self._initialized:
            raise RuntimeError(f"节点 {self.name} 未正确初始化")
        
        # 执行计算
        self.compute()
        
        # 返回输出
        return self.outputs.get_all_outputs()
    
    def set_inputs(self, **kwargs) -> None:
        """
        批量设置输入值
        
        Args:
            **kwargs: 输入参数键值对
        """
        for key, value in kwargs.items():
            self.inputs.set_input(key, value)
    
    def get_outputs(self) -> Dict[str, Any]:
        """
        获取所有输出值
        
        Returns:
            所有输出值的字典
        """
        return self.outputs.get_all_outputs()
    
    def reset(self) -> None:
        """
        重置节点状态（可选实现）
        """
        pass
    
    def __str__(self) -> str:
        return f"{self.__class__.__name__}(name='{self.name}')"
    
    def __repr__(self) -> str:
        return self.__str__()


class DatabaseMock:
    """
    模拟ActionGraph的数据库对象
    用于兼容原始ActionGraph代码
    """
    
    def __init__(self, inputs: NodeInputs, outputs: NodeOutputs):
        self.inputs = inputs
        self.outputs = outputs
