"""
四元数到欧拉角转换模块
从quat_to_euler.py转换而来
"""

import numpy as np
from .base_node import BaseNode


class QuatToEulerNode(BaseNode):
    """
    将四元数转换为欧拉角的节点
    
    输入:
        quaternion: 四元数 [x, y, z, w]
    
    输出:
        rotation: 欧拉角 [roll, pitch, yaw] (度)
    """
    
    def __init__(self, name: str = "QuatToEulerNode"):
        super().__init__(name)
    
    def setup(self) -> None:
        """初始化四元数转换节点"""
        # 无需特殊初始化
        pass
    
    def compute(self) -> None:
        """
        将四元数转换为欧拉角
        
        使用标准的四元数到欧拉角转换公式
        """
        # 获取四元数输入
        quaternion = self.inputs.get_input('quaternion', [0.0, 0.0, 0.0, 1.0])
        
        # 提取四元数分量
        x = quaternion[0]
        y = quaternion[1]
        z = quaternion[2]
        w = quaternion[3]
        
        # 计算Roll (绕X轴旋转)
        t0 = 2 * (w * x + y * z)
        t1 = 1 - 2 * (x * x + y * y)
        roll = np.arctan2(t0, t1)
        
        # 计算Pitch (绕Y轴旋转)
        t2 = 2 * (w * y - z * x)
        t2 = np.clip(t2, -1.0, 1.0)  # 防止数值误差导致的域错误
        pitch = np.arcsin(t2)
        
        # 计算Yaw (绕Z轴旋转)
        t3 = 2 * (w * z + x * y)
        t4 = 1 - 2 * (y * y + z * z)
        yaw = np.arctan2(t3, t4)
        
        # 转换为度
        roll = np.degrees(roll)
        pitch = np.degrees(pitch)
        yaw = np.degrees(yaw)
        
        # 设置输出
        self.outputs.rotation = [roll, pitch, yaw]
