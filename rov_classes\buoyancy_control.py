"""
浮力控制模块 - 简单的浮力计算
从buoyancy_control.py转换而来
"""

from .base_node import BaseNode


class BuoyancyControlNode(BaseNode):
    """
    计算简单浮力的节点（不考虑旋转）
    
    输入:
        Volume: 物体体积 (m³) - 注意大写V
        height: 物体高度 (m)
        z_position: 物体Z位置 (m)
    
    输出:
        z_force: Z方向的浮力 (N)
    """
    
    def __init__(self, name: str = "BuoyancyControlNode"):
        super().__init__(name)
    
    def setup(self) -> None:
        """初始化浮力控制节点"""
        # 定义常量
        self.water_density = 1.0  # kg/m³ 水密度
        self.gravity = 9.8  # m/s² 重力加速度
    
    def compute(self) -> None:
        """计算简单浮力（不旋转）"""
        # 获取输入参数（注意Volume是大写）
        volume = self.inputs.get_input('Volume', 1.0)  # m³
        height = self.inputs.get_input('height', 1.0)  # m
        z_position = self.inputs.get_input('z_position', 0.0)  # m
        
        # 计算浸没体积
        submerged_height = self._calculate_submerged_height(z_position, height)
        submerged_volume = volume * submerged_height
        
        # 计算浮力
        buoyancy_force = self.water_density * submerged_volume * self.gravity
        
        # 设置输出
        self.outputs.z_force = buoyancy_force
    
    def _calculate_submerged_height(self, z_position: float, height: float) -> float:
        """
        根据Z位置计算物体的浸没高度
        
        Args:
            z_position: 物体Z位置
            height: 物体总高度
        
        Returns:
            浸没高度比例 (0.0 到 1.0)
        """
        center_of_h = height / 2
        if z_position >= center_of_h:
            return 0.0
        elif z_position < -center_of_h:
            return 1.0
        else:
            return (center_of_h - z_position) / height
