"""
PID控制器模块
从controller.py转换而来
"""

from .base_node import BaseNode


class ControllerNode(BaseNode):
    """
    PID控制器节点，用于方向控制和潜水力控制
    
    输入:
        orientation: 当前方向测量值
        dive_force: 额外的垂直力
    
    输出:
        force: 正控制力向量 [x,y,z]
        minus_force: 负控制力向量 [x,y,z]
    """
    
    def __init__(self, name: str = "ControllerNode"):
        super().__init__(name)
    
    def setup(self) -> None:
        """初始化PID控制器参数和变量"""
        # PID参数
        self.sat_max = 1000  # 最大饱和限制
        self.sat_min = -1000  # 最小饱和限制
        self.kp = 100  # 比例增益
        self.ki = 10   # 积分增益
        self.kd = 0.01 # 微分增益
        
        # PID状态变量
        self.error_integral = 0  # 积分误差累积
        self.error_prev = 0      # 前一次误差
        
        # 时间步长
        self.time = 0.01667  # 约60Hz
    
    def compute(self) -> None:
        """
        计算PID控制输出用于方向控制和潜水力
        
        实现一个试图维持0方向的PID控制器
        控制输出在sat_min和sat_max之间饱和
        最终力包含额外的潜水力分量
        """
        # 获取输入
        orientation = self.inputs.get_input('orientation', 0.0)
        dive_force = self.inputs.get_input('dive_force', 0.0)
        
        # 计算误差（目标为0方向）
        error = 0 - orientation
        self.error_integral += error
        
        # PID控制计算
        control_output = (self.kp * error + 
                         self.ki * (self.error_integral) * self.time + 
                         self.kd * (error - self.error_prev) / self.time)
        
        # 饱和限制
        if control_output > self.sat_max:
            control_output = self.sat_max
        elif control_output < self.sat_min:
            control_output = self.sat_min
        
        # 更新前一次误差
        self.error_prev = error
        
        # 设置输出（包含潜水力）
        self.outputs.force = [0, 0, control_output + dive_force]
        self.outputs.minus_force = [0, 0, -control_output + dive_force]
    
    def reset(self) -> None:
        """重置PID控制器状态"""
        self.error_integral = 0
        self.error_prev = 0
