#!/usr/bin/env python3
"""
调试ROV路径
专门测试不同的ROV路径，找到正确的刚体路径
"""

# Isaac Sim标准初始化代码
from isaacsim import SimulationApp
simulation_app = SimulationApp({"headless": True})

import os
import omni.usd
from omni.isaac.core import World
from omni.isaac.core.utils.prims import get_prim_at_path
from omni.isaac.core.prims import RigidPrimView
import carb


def test_rov_paths():
    """测试不同的ROV路径"""
    
    # USD文件路径
    usd_path = "/home/<USER>/self_underwater_isaacsim/ROV_THRUSTERS.usd"
    
    if not os.path.exists(usd_path):
        carb.log_error(f"USD文件不存在: {usd_path}")
        return
    
    try:
        carb.log_info(f"加载USD文件: {usd_path}")
        
        # 加载USD文件
        context = omni.usd.get_context()
        context.open_stage(usd_path)
        
        # 等待加载完成
        for i in range(10):
            simulation_app.update()
        
        # 创建World对象
        world = World(stage_units_in_meters=1.0)
        world.reset()
        
        # 获取stage
        stage = context.get_stage()
        
        print("\n" + "="*80)
        print("ROV路径调试测试")
        print("="*80)
        
        # 1. 分析所有ROV相关对象
        print("\n📍 第1步: 分析ROV相关对象")
        rov_objects = []
        thruster_objects = []
        
        for prim in stage.Traverse():
            prim_path = prim.GetPath().pathString
            prim_type = prim.GetTypeName()
            
            if "/World/ROV" in prim_path:
                rov_objects.append((prim_path, prim_type))
                
                if "/Thrusters/" in prim_path:
                    thruster_objects.append((prim_path, prim_type))
        
        print(f"找到 {len(rov_objects)} 个ROV相关对象:")
        for prim_path, prim_type in rov_objects[:20]:  # 限制显示数量
            level = prim_path.count('/') - 2  # 相对于/World/ROV的层级
            indent = "  " * level
            print(f"{indent}├─ {prim_path} ({prim_type})")
        
        if len(rov_objects) > 20:
            print(f"  ... 还有 {len(rov_objects) - 20} 个对象")
        
        print(f"\n🚀 推进器对象 ({len(thruster_objects)}个):")
        for prim_path, prim_type in thruster_objects:
            print(f"  ├─ {prim_path} ({prim_type})")
        
        # 2. 测试不同的路径模式
        print(f"\n🧪 第2步: 测试RigidPrimView路径")
        
        test_paths = [
            "/World/ROV/Thrusters/*",        # 推进器通配符
            "/World/ROV/*",                  # 所有子对象通配符
            "/World/ROV",                    # 直接ROV对象
            "/World/ROV/Thrusters/left_front",   # 单个推进器
            "/World/ROV/Thrusters/right_front",
            "/World/ROV/Thrusters/left_back", 
            "/World/ROV/Thrusters/right_back",
        ]
        
        successful_paths = []
        
        for i, test_path in enumerate(test_paths):
            print(f"\n  测试路径 {i+1}/{len(test_paths)}: {test_path}")
            
            try:
                # 尝试创建RigidPrimView
                test_view = RigidPrimView(
                    prim_paths_expr=test_path,
                    name=f"test_view_{i}"
                )
                
                # 尝试添加到场景
                world.scene.add(test_view)
                
                # 尝试获取信息
                try:
                    count = test_view.count
                    print(f"    ✅ 成功！刚体数量: {count}")
                    
                    # 尝试获取位置信息
                    try:
                        poses = test_view.get_world_poses()
                        positions = poses[0]
                        if positions is not None and len(positions) > 0:
                            print(f"    📍 第一个刚体位置: {positions[0]}")
                        else:
                            print(f"    ⚠️  无法获取位置信息")
                    except Exception as e:
                        print(f"    ⚠️  获取位置失败: {e}")
                    
                    successful_paths.append((test_path, count))
                    
                except Exception as e:
                    print(f"    ⚠️  验证失败: {e}")
                    successful_paths.append((test_path, "未知"))
                
            except Exception as e:
                print(f"    ❌ 失败: {str(e)[:100]}...")
        
        # 3. 总结结果
        print(f"\n" + "="*80)
        print("测试结果总结")
        print("="*80)
        
        print(f"📊 统计信息:")
        print(f"  ROV相关对象: {len(rov_objects)}")
        print(f"  推进器对象: {len(thruster_objects)}")
        print(f"  成功的路径: {len(successful_paths)}")
        
        if successful_paths:
            print(f"\n🎯 可用的路径:")
            for i, (path, count) in enumerate(successful_paths, 1):
                print(f"  {i}. {path} (刚体数量: {count})")
            
            print(f"\n💡 建议:")
            best_path = successful_paths[0][0]
            print(f"  - 推荐使用路径: {best_path}")
            print(f"  - 在rov_standalone.py中设置此路径为首选")
        else:
            print(f"\n❌ 没有找到可用的路径")
            print(f"💡 可能的解决方案:")
            print(f"  - 检查USD文件中的物理设置")
            print(f"  - 确认推进器对象有RigidBodyAPI")
            print(f"  - 尝试在Isaac Sim中重新设置物理属性")
        
    except Exception as e:
        carb.log_error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🔍 ROV路径调试工具")
    print("="*50)
    
    try:
        test_rov_paths()
    except KeyboardInterrupt:
        print("\n⚠️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
    finally:
        simulation_app.close()


if __name__ == "__main__":
    main()
