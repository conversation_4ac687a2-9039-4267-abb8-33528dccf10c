"""
浮力计算模块 - 带旋转的浮力计算
从buoyancy_forces.py转换而来
"""

import math
import numpy as np
from .base_node import BaseNode


class BuoyancyForcesNode(BaseNode):
    """
    计算浮力并根据物体方向进行旋转的节点
    
    输入:
        volume: 物体体积 (m³)
        height: 物体高度 (m)
        z_position: 物体Z位置 (m)
        rotation: 旋转角度 [roll, pitch, yaw] (度)
    
    输出:
        x_force: X方向的浮力 (N)
        y_force: Y方向的浮力 (N)
        z_force: Z方向的浮力 (N)
    """
    
    def __init__(self, name: str = "BuoyancyForcesNode"):
        super().__init__(name)
    
    def setup(self) -> None:
        """初始化浮力计算节点"""
        # 定义常量
        self.water_density = 1.0  # kg/m³ 水密度
        self.gravity = 9.8  # m/s² 重力加速度
    
    def compute(self) -> None:
        """计算带旋转的浮力"""
        # 获取输入参数
        volume = self.inputs.get_input('volume', 1.0)  # m³
        height = self.inputs.get_input('height', 1.0)  # m
        z_position = self.inputs.get_input('z_position', 0.0)  # m
        rotation = self.inputs.get_input('rotation', [0.0, 0.0, 0.0])  # 度
        
        # 转换角度为弧度
        rotation_angles = np.deg2rad(rotation)
        
        # 计算浸没体积
        submerged_height = self._calculate_submerged_height(z_position, height)
        submerged_volume = volume * submerged_height
        
        # 计算浮力大小
        buoyancy_force = self.water_density * submerged_volume * self.gravity
        
        # 创建旋转矩阵
        roll_matrix = self._create_rotation_matrix_x(rotation_angles[0])
        pitch_matrix = self._create_rotation_matrix_y(rotation_angles[1])
        yaw_matrix = self._create_rotation_matrix_z(rotation_angles[2])
        
        # 组合旋转矩阵
        rotation_matrix = roll_matrix @ pitch_matrix @ yaw_matrix
        
        # 创建浮力向量并旋转
        buoyancy_vector = np.array([0, 0, buoyancy_force])
        rotated_buoyancy_vector = np.matmul(rotation_matrix, buoyancy_vector)
        
        # 设置输出（注意符号处理）
        self.outputs.x_force = -rotated_buoyancy_vector[0, 0]
        self.outputs.y_force = -rotated_buoyancy_vector[0, 1]
        self.outputs.z_force = rotated_buoyancy_vector[0, 2]
    
    def _calculate_submerged_height(self, z_position: float, height: float) -> float:
        """
        根据Z位置计算物体的浸没高度
        
        Args:
            z_position: 物体Z位置
            height: 物体总高度
        
        Returns:
            浸没高度比例 (0.0 到 1.0)
        """
        center_of_h = height / 2
        if z_position >= center_of_h:
            return 0.0
        elif z_position < -center_of_h:
            return 1.0
        else:
            return (center_of_h - z_position) / height
    
    def _create_rotation_matrix_x(self, angle: float) -> np.matrix:
        """
        创建绕X轴的旋转矩阵
        
        Args:
            angle: 旋转角度（弧度）
        
        Returns:
            3x3旋转矩阵
        """
        return np.matrix([
            [1, 0, 0],
            [0, math.cos(angle), -math.sin(angle)],
            [0, math.sin(angle), math.cos(angle)]
        ])
    
    def _create_rotation_matrix_y(self, angle: float) -> np.matrix:
        """
        创建绕Y轴的旋转矩阵
        
        Args:
            angle: 旋转角度（弧度）
        
        Returns:
            3x3旋转矩阵
        """
        return np.matrix([
            [math.cos(angle), 0, math.sin(angle)],
            [0, 1, 0],
            [-math.sin(angle), 0, math.cos(angle)]
        ])
    
    def _create_rotation_matrix_z(self, angle: float) -> np.matrix:
        """
        创建绕Z轴的旋转矩阵
        
        Args:
            angle: 旋转角度（弧度）
        
        Returns:
            3x3旋转矩阵
        """
        return np.matrix([
            [math.cos(angle), -math.sin(angle), 0],
            [math.sin(angle), math.cos(angle), 0],
            [0, 0, 1]
        ])
