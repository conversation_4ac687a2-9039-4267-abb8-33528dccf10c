# Isaac Sim环境设置指南

## 问题诊断

根据运行错误 `ModuleNotFoundError: No module named 'isaa<PERSON><PERSON>'`，这表明Isaac Sim没有正确安装或环境变量没有设置。

## 解决方案

### 方法1: 使用Isaac Sim内置Python环境

Isaac Sim通常自带Python环境，需要使用其内置的Python解释器：

#### Windows系统
```bash
# 找到Isaac Sim安装目录，通常在：
# C:\Users\<USER>\AppData\Local\ov\pkg\isaac_sim-{版本号}
# 或
# C:\Program Files\NVIDIA Corporation\Isaac Sim\{版本号}

# 使用Isaac Sim的Python运行脚本
cd "C:\Users\<USER>\AppData\Local\ov\pkg\isaac_sim-4.5.0"
python.bat E:\desk\ISSAC_SIM\isaac_underwater\rov_standalone.py
```

#### Linux系统
```bash
# 找到Isaac Sim安装目录，通常在：
# ~/.local/share/ov/pkg/isaac_sim-{版本号}

# 使用Isaac Sim的Python运行脚本
cd ~/.local/share/ov/pkg/isaac_sim-4.5.0
./python.sh /path/to/isaac_underwater/rov_standalone.py
```

### 方法2: 设置环境变量

如果想在任何地方运行，需要设置环境变量：

#### Windows系统
```batch
# 设置Isaac Sim路径
set ISAAC_SIM_PATH=C:\Users\<USER>\AppData\Local\ov\pkg\isaac_sim-4.5.0
set PYTHONPATH=%ISAAC_SIM_PATH%;%ISAAC_SIM_PATH%\site-packages;%PYTHONPATH%
set PATH=%ISAAC_SIM_PATH%;%PATH%

# 然后运行
python rov_standalone.py
```

#### Linux系统
```bash
# 设置Isaac Sim路径
export ISAAC_SIM_PATH=~/.local/share/ov/pkg/isaac_sim-4.5.0
export PYTHONPATH=$ISAAC_SIM_PATH:$ISAAC_SIM_PATH/site-packages:$PYTHONPATH
export PATH=$ISAAC_SIM_PATH:$PATH

# 然后运行
python rov_standalone.py
```

### 方法3: 使用Omniverse Launcher

1. 打开Omniverse Launcher
2. 启动Isaac Sim
3. 在Isaac Sim中打开Script Editor
4. 将`rov_standalone.py`的内容复制到Script Editor中运行

## 验证安装

运行以下命令验证Isaac Sim是否正确安装：

```python
# 测试脚本 - test_isaac_import.py
try:
    from isaacsim import SimulationApp
    print("✅ Isaac Sim导入成功")
    
    # 创建应用实例
    simulation_app = SimulationApp({"headless": True})
    print("✅ SimulationApp创建成功")
    
    # 导入其他Isaac模块
    from omni.isaac.core import World
    print("✅ Isaac Core导入成功")
    
    import omni.usd
    print("✅ Omni USD导入成功")
    
    print("🎉 所有模块导入成功，Isaac Sim环境正常！")
    
    simulation_app.close()
    
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    print("请检查Isaac Sim安装和环境变量设置")
```

## 运行ROV仿真

### 1. 准备工作
确保以下文件在同一目录下：
- `rov_standalone.py` - 主运行文件
- `rov_classes/` - ROV功能类目录
- `ROV_THRUSTERS.usd` - ROV场景文件

### 2. 运行命令

#### 使用Isaac Sim Python环境（推荐）
```bash
# Windows
cd "C:\Users\<USER>\AppData\Local\ov\pkg\isaac_sim-4.5.0"
python.bat E:\desk\ISSAC_SIM\isaac_underwater\rov_standalone.py

# Linux
cd ~/.local/share/ov/pkg/isaac_sim-4.5.0
./python.sh /path/to/isaac_underwater/rov_standalone.py
```

#### 使用环境变量设置后
```bash
cd E:\desk\ISSAC_SIM\isaac_underwater
python rov_standalone.py
```

### 3. 预期结果
如果一切正常，应该看到：
```
2025-08-02 XX:XX:XX [Info] [__main__] 成功加载USD文件: ROV_THRUSTERS.usd
2025-08-02 XX:XX:XX [Info] [__main__] ROV设置完成
2025-08-02 XX:XX:XX [Info] [__main__] ROV仿真启动成功，开始仿真循环...
```

## 替代方案

如果Isaac Sim环境设置困难，可以使用我们提供的简化版本：

```bash
# 运行简化版本（不需要Isaac Sim）
python rov_standalone_simple.py

# 运行功能测试
python test_rov_classes.py
```

这些版本可以验证ROV类的功能是否正常，不依赖Isaac Sim环境。

## 常见问题

### Q1: 找不到Isaac Sim安装目录
**A**: 检查以下位置：
- Windows: `C:\Users\<USER>\AppData\Local\ov\pkg\`
- Linux: `~/.local/share/ov/pkg/`
- 或通过Omniverse Launcher查看安装路径

### Q2: USD文件加载失败
**A**: 确保：
- `ROV_THRUSTERS.usd`文件存在
- 文件路径正确
- 文件没有损坏

### Q3: ROV对象未找到
**A**: 检查USD文件中是否包含`/World/ROV`路径的对象

### Q4: 性能问题
**A**: 尝试：
- 使用headless模式：`SimulationApp({"headless": True})`
- 降低仿真频率
- 检查系统资源使用情况

## 技术支持

如果遇到问题，请提供：
1. Isaac Sim版本号
2. 操作系统版本
3. 完整的错误信息
4. Python版本信息

这将帮助快速诊断和解决问题。
