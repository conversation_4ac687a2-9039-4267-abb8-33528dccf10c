#!/usr/bin/env python3
"""
测试USD文件加载功能
简化版本，用于验证基本的USD加载和World创建
"""

# Isaac Sim标准初始化代码
from isaacsim import SimulationApp
simulation_app = SimulationApp({"headless": False})

import os
import omni.usd
from omni.isaac.core import World
from omni.isaac.core.utils.prims import get_prim_at_path
import carb


def test_usd_loading():
    """测试USD文件加载"""
    
    # 检查USD文件是否存在
    usd_path = os.path.abspath("ROV_THRUSTERS.usd")
    if not os.path.exists(usd_path):
        carb.log_error(f"USD文件不存在: {usd_path}")
        return False
    
    try:
        carb.log_info(f"开始加载USD文件: {usd_path}")
        
        # 使用omni.usd.get_context().open_stage()打开USD文件
        context = omni.usd.get_context()
        context.open_stage(usd_path)
        carb.log_info("USD文件加载成功")
        
        # 等待几帧让USD完全加载
        for i in range(10):
            simulation_app.update()
        
        # 创建World对象
        carb.log_info("创建World对象...")
        world = World(stage_units_in_meters=1.0)
        carb.log_info("World对象创建成功")
        
        # 重置世界
        carb.log_info("重置世界...")
        world.reset()
        carb.log_info("世界重置成功")
        
        # 检查ROV对象是否存在
        rov_prim_path = "/World/ROV"
        rov_prim = get_prim_at_path(rov_prim_path)
        
        if rov_prim is None or not rov_prim.IsValid():
            carb.log_warn(f"ROV对象不存在或无效: {rov_prim_path}")
            
            # 列出所有可用的prim路径
            carb.log_info("列出场景中的主要对象:")
            stage = context.get_stage()
            for prim in stage.Traverse():
                if prim.GetPath().pathString.count('/') <= 2:  # 只显示前两级
                    carb.log_info(f"  - {prim.GetPath()}")
        else:
            carb.log_info(f"找到ROV对象: {rov_prim_path}")
        
        # 运行几秒钟的仿真
        carb.log_info("开始短暂仿真测试...")
        frame_count = 0
        max_frames = 300  # 约5秒 (60fps)
        
        while simulation_app.is_running() and frame_count < max_frames:
            world.step(render=True)
            frame_count += 1
            
            if frame_count % 60 == 0:  # 每秒打印一次
                carb.log_info(f"仿真运行中... 帧数: {frame_count}")
        
        carb.log_info("测试完成")
        return True
        
    except Exception as e:
        carb.log_error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    try:
        success = test_usd_loading()
        if success:
            carb.log_info("USD加载测试成功！")
        else:
            carb.log_error("USD加载测试失败！")
    except KeyboardInterrupt:
        carb.log_info("用户中断测试")
    except Exception as e:
        carb.log_error(f"测试错误: {e}")
    finally:
        simulation_app.close()


if __name__ == "__main__":
    main()
