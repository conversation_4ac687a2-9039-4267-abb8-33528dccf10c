# ROV Standalone模式使用说明

本项目将原有的ActionGraph代码转换为Python类，实现了standalone模式下的ROV水下仿真。

## 项目结构

```
isaac_underwater/
├── rov_classes/                 # ROV功能类模块
│   ├── __init__.py             # 模块初始化
│   ├── base_node.py            # 基础节点类
│   ├── buoyancy_forces.py      # 浮力计算（带旋转）
│   ├── buoyancy_control.py     # 简单浮力计算
│   ├── damping.py              # 阻尼计算
│   ├── controller.py           # PID控制器
│   ├── linear_angular_control.py # 推进器控制
│   └── quat_to_euler.py        # 四元数转欧拉角
├── rov_standalone.py           # 主运行文件
├── test_rov_classes.py         # 功能测试脚本
└── ROV_THRUSTERS.usd          # ROV场景文件
```

## 功能模块说明

### 1. BuoyancyForcesNode - 浮力计算（带旋转）
- **功能**: 计算考虑物体旋转的浮力
- **输入**: volume(体积), height(高度), z_position(Z位置), rotation(旋转角度)
- **输出**: x_force, y_force, z_force(三轴浮力)

### 2. BuoyancyControlNode - 简单浮力计算
- **功能**: 计算不考虑旋转的简单浮力
- **输入**: Volume(体积), height(高度), z_position(Z位置)
- **输出**: z_force(Z轴浮力)

### 3. DampingNode - 阻尼计算
- **功能**: 根据物体位置计算阻尼力
- **输入**: z_position(Z位置), max_damping(最大阻尼), floating_obj_height(物体高度)
- **输出**: linear_damping(线性阻尼), angular_damping(角阻尼)

### 4. ControllerNode - PID控制器
- **功能**: 实现PID控制器用于姿态稳定
- **输入**: orientation(当前姿态), dive_force(潜水力)
- **输出**: force(正控制力), minus_force(负控制力)

### 5. LinearAngularControlNode - 推进器控制
- **功能**: 根据摇杆输入计算四个推进器的力
- **输入**: y_stick(Y轴摇杆), x_stick(X轴摇杆)
- **输出**: left_front, right_front, left_back, right_back(四个推进器力)

### 6. QuatToEulerNode - 四元数转换
- **功能**: 将四元数转换为欧拉角
- **输入**: quaternion(四元数[x,y,z,w])
- **输出**: rotation(欧拉角[roll,pitch,yaw])

## 使用方法

### 1. 环境准备
**重要**: 需要正确安装Isaac Sim环境。如果遇到`ModuleNotFoundError: No module named 'isaacsim'`错误，请参考`ISAAC_SIM_SETUP_GUIDE.md`文件。

#### 快速环境设置（Windows）
```bash
# 使用Isaac Sim内置Python环境（推荐）
cd "C:\Users\<USER>\AppData\Local\ov\pkg\isaac_sim-4.5.0"
python.bat E:\desk\ISSAC_SIM\isaac_underwater\rov_standalone.py
```

### 2. 测试功能
运行测试脚本验证所有模块功能：
```bash
python test_rov_classes.py
```

### 3. 运行Standalone仿真
```bash
python rov_standalone.py
```

**注意**: 确保ROV_THRUSTERS.usd文件在当前目录下。

### 4. 简化版本测试（无需Isaac Sim）
如果Isaac Sim环境设置困难，可以先运行简化版本验证功能：
```bash
python rov_standalone_simple.py
```

### 3. 自定义使用
```python
from rov_classes import BuoyancyForcesNode, ControllerNode

# 创建节点
buoyancy_node = BuoyancyForcesNode("MyBuoyancy")
controller_node = ControllerNode("MyController")

# 设置输入
buoyancy_node.set_inputs(
    volume=0.1,
    height=0.2,
    z_position=-0.05,
    rotation=[10.0, 5.0, 0.0]
)

# 执行计算
buoyancy_output = buoyancy_node.execute()
print(f"浮力: {buoyancy_output}")
```

## 主要特性

### 1. 统一的节点接口
- 所有节点继承自`BaseNode`基类
- 统一的`setup()`和`compute()`方法
- 标准化的输入输出处理

### 2. 兼容原ActionGraph逻辑
- 保持与原ActionGraph代码相同的计算逻辑
- 相同的输入输出参数名称
- 相同的物理计算公式

### 3. 易于扩展
- 模块化设计，易于添加新功能
- 清晰的接口定义
- 完整的错误处理

## 与原ActionGraph的对比

| 特性 | ActionGraph | Python类 |
|------|-------------|----------|
| 运行模式 | 图形化节点 | Standalone代码 |
| 调试难度 | 较难 | 容易 |
| 代码复用 | 受限 | 灵活 |
| 性能 | 图形开销 | 纯Python |
| 扩展性 | 受限 | 高 |

## 技术细节

### 1. 坐标系统
- 使用右手坐标系
- Z轴向上为正
- 水面位置为Z=0

### 2. 物理参数
- 水密度: 1.0 kg/m³
- 重力加速度: 9.8 m/s²
- PID参数: kp=100, ki=10, kd=0.01

### 3. 旋转处理
- 使用欧拉角(roll, pitch, yaw)
- 旋转矩阵组合顺序: Roll × Pitch × Yaw
- 角度单位: 度(输入) → 弧度(计算)

## 故障排除

### 1. 导入错误
确保rov_classes目录在Python路径中：
```python
import sys
sys.path.append('.')
```

### 2. USD文件加载失败
- 检查文件路径是否正确
- 确保Isaac Sim正确安装
- 验证USD文件格式

### 3. 仿真性能问题
- 调整仿真步长
- 优化力计算频率
- 检查内存使用

## 下一步开发

1. **手柄输入集成**: 添加PS4/Xbox手柄支持
2. **可视化增强**: 添加力向量显示
3. **参数调优**: 提供GUI参数调整界面
4. **多ROV支持**: 扩展支持多个ROV同时仿真
5. **物理精度**: 改进水动力学模型

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目遵循MIT许可证。
