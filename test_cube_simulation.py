#!/usr/bin/env python3
"""
测试立方体仿真 - 验证ActionGraph转换
验证ROV_TEST.usd文件和立方体对象的物理仿真是否正确工作
"""

import os
import sys
import numpy as np

# 添加当前目录到路径
sys.path.append('.')

def test_cube_physics_classes():
    """测试立方体物理类（不需要Isaac Sim）"""
    print("=== 测试立方体物理类 ===")
    
    try:
        from rov_classes import (
            BuoyancyForcesNode,
            DampingNode,
            ControllerNode,
            QuatToEulerNode
        )
        
        print("✅ 成功导入所有ROV类")
        
        # 1. 测试浮力计算
        print("\n1. 测试浮力计算...")
        buoyancy_node = BuoyancyForcesNode("CubeBuoyancy")
        
        # 模拟立方体参数
        cube_volume = 1.0  # 1m³ 立方体
        cube_height = 1.0  # 1m 高度
        cube_z_position = -0.3  # 部分浸没在水中
        cube_rotation = [5.0, 10.0, 0.0]  # 轻微倾斜
        
        buoyancy_node.set_inputs(
            volume=cube_volume,
            height=cube_height,
            z_position=cube_z_position,
            rotation=cube_rotation
        )
        
        buoyancy_output = buoyancy_node.execute()
        print(f"  立方体浮力: x={buoyancy_output.get('x_force', 0):.3f}N, "
              f"y={buoyancy_output.get('y_force', 0):.3f}N, "
              f"z={buoyancy_output.get('z_force', 0):.3f}N")
        
        # 2. 测试阻尼计算
        print("\n2. 测试阻尼计算...")
        damping_node = DampingNode("CubeDamping")
        
        damping_node.set_inputs(
            z_position=cube_z_position,
            max_damping=2.0,
            floating_obj_height=cube_height
        )
        
        damping_output = damping_node.execute()
        print(f"  立方体阻尼: linear={damping_output.get('linear_damping', 0):.3f}, "
              f"angular={damping_output.get('angular_damping', 0):.3f}")
        
        # 3. 测试四元数转换
        print("\n3. 测试四元数转换...")
        quat_node = QuatToEulerNode("CubeQuat")
        
        # 模拟四元数（轻微旋转）
        test_quaternion = [0.9659, 0.0436, 0.0872, 0.2419]  # 约15度旋转
        
        quat_node.set_inputs(quaternion=test_quaternion)
        euler_output = quat_node.execute()
        rotation = euler_output.get('rotation', [0, 0, 0])
        print(f"  欧拉角: roll={rotation[0]:.1f}°, pitch={rotation[1]:.1f}°, yaw={rotation[2]:.1f}°")
        
        # 4. 测试控制器
        print("\n4. 测试PID控制器...")
        controller_node = ControllerNode("CubeController")
        
        controller_node.set_inputs(
            orientation=rotation[1],  # 使用pitch角
            dive_force=0.5
        )
        
        controller_output = controller_node.execute()
        force = controller_output.get('force', [0, 0, 0])
        minus_force = controller_output.get('minus_force', [0, 0, 0])
        print(f"  控制力: force=[{force[0]:.2f}, {force[1]:.2f}, {force[2]:.2f}]")
        print(f"  反向力: minus_force=[{minus_force[0]:.2f}, {minus_force[1]:.2f}, {minus_force[2]:.2f}]")
        
        print("\n✅ 所有立方体物理类测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_usd_file_existence():
    """测试USD文件是否存在"""
    print("\n=== 测试USD文件 ===")
    
    usd_files = [
        "ROV_TEST.usd",
        "BUOYANCY_TEST.usda"
    ]
    
    all_exist = True
    for usd_file in usd_files:
        if os.path.exists(usd_file):
            size_mb = os.path.getsize(usd_file) / (1024 * 1024)
            print(f"✅ {usd_file} 存在 ({size_mb:.1f} MB)")
        else:
            print(f"❌ {usd_file} 不存在")
            all_exist = False
    
    return all_exist


def test_actiongraph_conversion():
    """测试ActionGraph转换的完整性"""
    print("\n=== 测试ActionGraph转换 ===")
    
    try:
        # 模拟完整的物理计算流程
        from rov_classes import (
            BuoyancyForcesNode,
            DampingNode,
            ControllerNode,
            QuatToEulerNode
        )
        
        # 创建所有节点
        buoyancy_node = BuoyancyForcesNode("TestBuoyancy")
        damping_node = DampingNode("TestDamping")
        controller_node = ControllerNode("TestController")
        quat_node = QuatToEulerNode("TestQuat")
        
        print("✅ 所有ActionGraph节点成功创建")
        
        # 模拟一个完整的仿真步骤
        print("\n模拟仿真步骤:")
        
        # 立方体状态
        cube_state = {
            'position': [0.0, 0.0, -0.2],  # 部分浸没
            'orientation': [0.9659, 0.0436, 0.0872, 0.2419],  # 四元数
            'velocity': [0.1, 0.0, -0.05],  # 轻微运动
        }
        
        # 1. 四元数转欧拉角
        quat_node.set_inputs(quaternion=cube_state['orientation'])
        euler_result = quat_node.execute()
        rotation = euler_result.get('rotation', [0, 0, 0])
        
        # 2. 计算浮力
        buoyancy_node.set_inputs(
            volume=1.0,
            height=1.0,
            z_position=cube_state['position'][2],
            rotation=rotation
        )
        buoyancy_result = buoyancy_node.execute()
        
        # 3. 计算阻尼
        damping_node.set_inputs(
            z_position=cube_state['position'][2],
            max_damping=2.0,
            floating_obj_height=1.0
        )
        damping_result = damping_node.execute()
        
        # 4. 计算控制
        controller_node.set_inputs(
            orientation=rotation[1],  # pitch
            dive_force=0.0
        )
        controller_result = controller_node.execute()
        
        # 输出结果
        print(f"  旋转角度: {rotation}")
        print(f"  浮力: {buoyancy_result}")
        print(f"  阻尼: {damping_result}")
        print(f"  控制: {controller_result}")
        
        print("\n✅ ActionGraph转换测试完成！")
        return True
        
    except Exception as e:
        print(f"❌ ActionGraph转换测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🧪 立方体仿真测试 - ActionGraph转换验证")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        ("USD文件存在性", test_usd_file_existence),
        ("立方体物理类", test_cube_physics_classes),
        ("ActionGraph转换", test_actiongraph_conversion),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔍 运行测试: {test_name}")
        result = test_func()
        results.append((test_name, result))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(tests)} 测试通过")
    
    if passed == len(tests):
        print("🎉 所有测试通过！立方体仿真准备就绪。")
        print("\n📝 下一步:")
        print("  1. 在Isaac Sim中运行: python rov_standalone.py")
        print("  2. 验证立方体物理行为与ActionGraph一致")
        print("  3. 检查浮力和阻尼效果")
    else:
        print("⚠️  部分测试失败，请检查问题后重试。")


if __name__ == "__main__":
    main()
