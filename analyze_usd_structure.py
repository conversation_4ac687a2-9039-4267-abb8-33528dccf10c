#!/usr/bin/env python3
"""
USD场景结构分析工具
用于分析ROV_THRUSTERS.usd文件的结构，找到正确的刚体对象路径
"""

# Isaac Sim标准初始化代码
from isaacsim import SimulationApp
simulation_app = SimulationApp({"headless": True})

import os
import omni.usd
from omni.isaac.core import World
from omni.isaac.core.utils.prims import get_prim_at_path
from omni.isaac.core.prims import RigidPrimView
import carb


def analyze_usd_file(usd_path: str):
    """详细分析USD文件结构"""
    
    if not os.path.exists(usd_path):
        carb.log_error(f"USD文件不存在: {usd_path}")
        return
    
    try:
        carb.log_info(f"开始分析USD文件: {usd_path}")
        
        # 加载USD文件
        context = omni.usd.get_context()
        context.open_stage(usd_path)
        
        # 等待加载完成
        for i in range(10):
            simulation_app.update()
        
        # 创建World对象
        world = World(stage_units_in_meters=1.0)
        world.reset()
        
        # 获取stage
        stage = context.get_stage()
        
        print("\n" + "="*80)
        print("USD场景结构分析")
        print("="*80)
        
        # 分析所有prim
        all_prims = []
        rov_prims = []
        
        for prim in stage.Traverse():
            prim_path = prim.GetPath().pathString
            prim_type = prim.GetTypeName()
            
            all_prims.append((prim_path, prim_type))
            
            if "/World/ROV" in prim_path:
                rov_prims.append((prim_path, prim_type))
        
        # 打印所有prim（限制层级）
        print(f"\n📁 所有场景对象 (共{len(all_prims)}个):")
        for prim_path, prim_type in all_prims:
            level = prim_path.count('/') - 1
            if level <= 3:  # 只显示前3级
                indent = "  " * level
                print(f"{indent}├─ {prim_path} ({prim_type})")
        
        # 详细分析ROV相关对象
        print(f"\n🤖 ROV相关对象 (共{len(rov_prims)}个):")
        rigid_body_candidates = []
        
        for prim_path, prim_type in rov_prims:
            print(f"  📍 {prim_path}")
            print(f"     类型: {prim_type}")
            
            # 获取prim对象
            prim = get_prim_at_path(prim_path)
            if prim and prim.IsValid():
                # 检查属性
                attributes = prim.GetAttributes()
                print(f"     属性数量: {len(attributes)}")
                
                # 检查是否可能是刚体
                is_rigid_candidate = False
                
                # 基于类型名判断
                if prim_type in ["RigidBody", "Cube", "Sphere", "Cylinder", "Mesh", "Xform"]:
                    is_rigid_candidate = True
                    print(f"     ✅ 基于类型判断可能是刚体")
                
                # 检查是否有物理属性
                physics_attrs = []
                for attr in attributes:
                    attr_name = attr.GetName()
                    if any(keyword in attr_name.lower() for keyword in ['physics', 'rigid', 'mass', 'collision']):
                        physics_attrs.append(attr_name)
                
                if physics_attrs:
                    print(f"     🔧 物理相关属性: {physics_attrs}")
                    is_rigid_candidate = True
                
                if is_rigid_candidate:
                    rigid_body_candidates.append(prim_path)
                    print(f"     ⭐ 标记为刚体候选")
                
                print()
        
        # 测试RigidPrimView创建
        print("🧪 测试RigidPrimView创建:")
        test_paths = [
            "/World/ROV",
            "/World/ROV/*"
        ] + rigid_body_candidates
        
        successful_paths = []
        
        for test_path in test_paths:
            try:
                print(f"  测试路径: {test_path}")
                
                # 尝试创建RigidPrimView
                test_view = RigidPrimView(
                    prim_paths_expr=test_path,
                    name=f"test_view_{len(successful_paths)}"
                )
                
                # 尝试添加到场景
                world.scene.add(test_view)
                
                print(f"    ✅ 成功创建RigidPrimView")
                successful_paths.append(test_path)
                
                # 尝试获取一些基本信息
                try:
                    count = test_view.count
                    print(f"    📊 刚体数量: {count}")
                except:
                    print(f"    ⚠️  无法获取刚体数量")
                
            except Exception as e:
                print(f"    ❌ 失败: {e}")
        
        # 总结
        print("\n" + "="*80)
        print("分析总结")
        print("="*80)
        print(f"📊 总prim数量: {len(all_prims)}")
        print(f"🤖 ROV相关对象: {len(rov_prims)}")
        print(f"⭐ 刚体候选: {len(rigid_body_candidates)}")
        print(f"✅ 可用路径: {len(successful_paths)}")
        
        if successful_paths:
            print(f"\n🎯 推荐使用的路径:")
            for i, path in enumerate(successful_paths, 1):
                print(f"  {i}. {path}")
        else:
            print(f"\n❌ 未找到可用的刚体路径")
        
        print("\n💡 建议:")
        if successful_paths:
            print(f"  - 在rov_standalone.py中使用路径: {successful_paths[0]}")
        else:
            print(f"  - 检查USD文件是否包含正确的物理设置")
            print(f"  - 确认ROV对象具有RigidBodyAPI")
            print(f"  - 考虑在Isaac Sim中重新设置物理属性")
        
    except Exception as e:
        carb.log_error(f"分析失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        simulation_app.close()


def main():
    """主函数"""
    # 使用用户修改后的路径
    usd_path = "/home/<USER>/self_underwater_isaacsim/ROV_THRUSTERS.usd"
    
    if not os.path.exists(usd_path):
        print(f"❌ USD文件不存在: {usd_path}")
        print("请确认文件路径是否正确")
        return
    
    analyze_usd_file(usd_path)


if __name__ == "__main__":
    main()
