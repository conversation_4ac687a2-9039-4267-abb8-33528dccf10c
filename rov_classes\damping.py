"""
阻尼计算模块
从damping.py转换而来
"""

from .base_node import BaseNode


class DampingNode(BaseNode):
    """
    根据物体位置计算阻尼力的节点
    
    输入:
        z_position: 物体Z位置 (m)
        max_damping: 最大阻尼值
        floating_obj_height: 浮动物体高度 (m)
    
    输出:
        linear_damping: 线性阻尼
        angular_damping: 角阻尼
    """
    
    def __init__(self, name: str = "DampingNode"):
        super().__init__(name)
    
    def setup(self) -> None:
        """初始化阻尼计算节点"""
        # 无需特殊初始化
        pass
    
    def compute(self) -> None:
        """计算基于位置的阻尼力"""
        # 获取输入参数
        z_pos = self.inputs.get_input('z_position', 0.0)  # m
        max_damp = self.inputs.get_input('max_damping', 1.0)
        height = self.inputs.get_input('floating_obj_height', 1.0)  # m
        
        # 计算半高度
        half_height = height / 2
        
        # 计算位移百分比
        displacement_percentage = -z_pos / height + 0.5
        
        # 根据位置计算阻尼
        damping = 0.0
        
        if z_pos >= half_height:
            # 物体在水面上方
            damping = 0.01
        elif z_pos < -half_height:
            # 物体完全浸没
            damping = max_damp
        elif -half_height <= z_pos < half_height:
            # 物体部分浸没
            damping = max_damp * displacement_percentage
        
        # 设置输出
        self.outputs.linear_damping = damping
        self.outputs.angular_damping = damping
