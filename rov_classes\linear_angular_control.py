"""
线性角度控制模块 - 推进器控制
从linear_angular_control.py转换而来
"""

from .base_node import BaseNode


class LinearAngularControlNode(BaseNode):
    """
    基于摇杆输入计算四个推进器线性力的节点
    
    该功能接收x和y摇杆值，并计算四个推进器的适当推力值，
    配置允许线性和角运动控制。
    
    输入:
        y_stick: Y轴摇杆值，用于前进/后退运动
        x_stick: X轴摇杆值，用于旋转运动
    
    输出:
        left_front: 左前推进器力向量 [x,y,z]
        right_front: 右前推进器力向量 [x,y,z]
        left_back: 左后推进器力向量 [x,y,z]
        right_back: 右后推进器力向量 [x,y,z]
    """
    
    def __init__(self, name: str = "LinearAngularControlNode"):
        super().__init__(name)
    
    def setup(self) -> None:
        """初始化推进器控制节点"""
        # 无需特殊初始化
        pass
    
    def compute(self) -> None:
        """根据摇杆输入计算四个推进器的线性力"""
        # 获取摇杆输入
        y_stick = self.inputs.get_input('y_stick', 0.0)
        x_stick = self.inputs.get_input('x_stick', 0.0)
        
        # 计算线性力
        # 推进器配置：
        # - y_stick控制前进/后退
        # - x_stick控制左右旋转
        # - 前推进器和后推进器方向相反以实现旋转
        
        self.outputs.left_front = [0, 0, y_stick + x_stick]
        self.outputs.right_front = [0, 0, y_stick - x_stick]
        self.outputs.left_back = [0, 0, -y_stick - x_stick]
        self.outputs.right_back = [0, 0, -y_stick + x_stick]
